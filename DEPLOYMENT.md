# 小红书文案生成系统部署指南

## 系统要求

### 服务器环境
- **操作系统**: Linux (推荐 Ubuntu 20.04+ 或 CentOS 7+) 或 Windows Server
- **Python**: 3.8 或更高版本
- **数据库**: MySQL 5.7 或更高版本
- **内存**: 最少 2GB，推荐 4GB+
- **存储**: 最少 10GB 可用空间

### 软件依赖
- Python 3.8+
- pip (Python 包管理器)
- MySQL 服务器
- Git (用于代码部署)

## 部署步骤

### 1. 准备服务器环境

#### Ubuntu/Debian 系统
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装 Python 3.8+ 和相关工具
sudo apt install python3 python3-pip python3-venv python3-dev -y

# 安装 MySQL 服务器
sudo apt install mysql-server mysql-client -y

# 安装其他必要工具
sudo apt install git curl wget build-essential -y

# 安装图片处理库依赖
sudo apt install libjpeg-dev zlib1g-dev libpng-dev -y
```

#### CentOS/RHEL 系统
```bash
# 更新系统包
sudo yum update -y

# 安装 Python 3.8+ 和相关工具
sudo yum install python3 python3-pip python3-devel -y

# 安装 MySQL 服务器
sudo yum install mysql-server mysql -y

# 安装其他必要工具
sudo yum install git curl wget gcc gcc-c++ make -y

# 安装图片处理库依赖
sudo yum install libjpeg-devel zlib-devel libpng-devel -y
```

### 2. 配置 MySQL 数据库

```bash
# 启动 MySQL 服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置 MySQL
sudo mysql_secure_installation

# 登录 MySQL 创建数据库和用户
sudo mysql -u root -p
```

在 MySQL 中执行以下命令：
```sql
-- 创建数据库
CREATE DATABASE xhsrw666 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER 'xhsrw666'@'localhost' IDENTIFIED BY 'xhsrw666';
GRANT ALL PRIVILEGES ON xhsrw666.* TO 'xhsrw666'@'localhost';
FLUSH PRIVILEGES;

-- 退出 MySQL
EXIT;
```

### 3. 部署应用代码

```bash
# 创建应用目录
sudo mkdir -p /opt/xiaohongshu
cd /opt/xiaohongshu

# 克隆或上传代码
# 方法1: 使用 Git (如果有代码仓库)
git clone <your-repository-url> .

# 方法2: 手动上传代码文件
# 将项目文件上传到 /opt/xiaohongshu 目录

# 设置目录权限
sudo chown -R $USER:$USER /opt/xiaohongshu
```

### 4. 创建 Python 虚拟环境

```bash
# 进入项目目录
cd /opt/xiaohongshu

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级 pip
pip install --upgrade pip
```

### 5. 安装 Python 依赖

```bash
# 确保在虚拟环境中
source venv/bin/activate

# 安装项目依赖
pip install -r requirements.txt

# 如果某些包安装失败，可以单独安装核心依赖
pip install Flask==3.1.1
pip install Flask-SQLAlchemy==3.1.1
pip install PyMySQL==1.1.0
pip install Flask-Login==0.6.3
pip install Flask-WTF==1.2.1
pip install Flask-CORS==4.0.0
pip install Pillow==10.1.0
pip install APScheduler==3.10.4
pip install pandas==2.1.4
pip install python-dateutil==2.8.2
pip install requests==2.31.0
```

### 6. 配置应用

```bash
# 复制配置文件模板（如果有）
cp app/config/config.py.example app/config/config.py

# 编辑配置文件
nano app/config/config.py
```

确保数据库配置正确：
```python
DB_CONFIG = {
    'ENGINE': 'mysql',
    'DRIVER': 'pymysql',
    'HOST': 'localhost',
    'USER': 'xhsrw666',
    'PASSWORD': 'xhsrw666',
    'NAME': 'xhsrw666',
    'CHARSET': 'utf8mb4'
}
```

### 7. 初始化数据库

```bash
# 确保在虚拟环境中
source venv/bin/activate

# 设置 Flask 应用环境变量
export FLASK_APP=run.py
export FLASK_ENV=production

# 初始化数据库
flask init-mysql

# 创建管理员账号
flask create-admin
```

### 8. 测试应用

```bash
# 在虚拟环境中启动应用
source venv/bin/activate
export FLASK_APP=run.py
export FLASK_ENV=production

# 测试启动
python run.py
```

如果看到类似以下输出，说明启动成功：
```
当前使用的数据库URI: mysql+pymysql://xhsrw666:***@localhost/xhsrw666?charset=utf8mb4
 * Running on http://127.0.0.1:5000
```

### 9. 配置生产环境运行

#### 方法1: 使用 Gunicorn (推荐)

```bash
# 安装 Gunicorn
pip install gunicorn

# 创建 Gunicorn 配置文件
cat > gunicorn.conf.py << EOF
bind = "0.0.0.0:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
EOF

# 启动应用
gunicorn -c gunicorn.conf.py run:app
```

#### 方法2: 使用 systemd 服务

```bash
# 创建 systemd 服务文件
sudo cat > /etc/systemd/system/xiaohongshu.service << EOF
[Unit]
Description=XiaoHongShu Content Generator
After=network.target mysql.service

[Service]
Type=exec
User=$USER
WorkingDirectory=/opt/xiaohongshu
Environment=PATH=/opt/xiaohongshu/venv/bin
Environment=FLASK_APP=run.py
Environment=FLASK_ENV=production
ExecStart=/opt/xiaohongshu/venv/bin/gunicorn -c gunicorn.conf.py run:app
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 重新加载 systemd 配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start xiaohongshu
sudo systemctl enable xiaohongshu

# 检查服务状态
sudo systemctl status xiaohongshu
```

### 10. 配置反向代理 (可选)

如果需要使用域名或 80/443 端口，可以配置 Nginx：

```bash
# 安装 Nginx
sudo apt install nginx -y  # Ubuntu/Debian
# 或
sudo yum install nginx -y  # CentOS/RHEL

# 创建 Nginx 配置
sudo cat > /etc/nginx/sites-available/xiaohongshu << EOF
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /static {
        alias /opt/xiaohongshu/app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/xiaohongshu /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 故障排除

### 常见问题

1. **Flask 命令找不到**
   ```bash
   # 确保激活了虚拟环境
   source venv/bin/activate
   # 设置环境变量
   export FLASK_APP=run.py
   ```

2. **数据库连接失败**
   - 检查 MySQL 服务是否运行: `sudo systemctl status mysql`
   - 检查数据库配置是否正确
   - 检查用户权限是否正确

3. **图片上传失败**
   - 检查上传目录权限: `chmod 755 app/static/uploads`
   - 确保安装了 Pillow 库

4. **端口被占用**
   ```bash
   # 查看端口占用
   sudo netstat -tlnp | grep :5000
   # 或使用其他端口
   gunicorn -b 0.0.0.0:8000 -c gunicorn.conf.py run:app
   ```

### 日志查看

```bash
# 查看应用日志
sudo journalctl -u xiaohongshu -f

# 查看 Nginx 日志
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

## 维护和备份

### 数据库备份
```bash
# 创建备份脚本
cat > backup.sh << EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
mysqldump -u xhsrw666 -p xhsrw666 > backup_\$DATE.sql
EOF

chmod +x backup.sh
```

### 定期维护
- 定期备份数据库
- 监控磁盘空间
- 检查应用日志
- 更新系统安全补丁

## 安全建议

1. 修改默认数据库密码
2. 配置防火墙规则
3. 使用 HTTPS (配置 SSL 证书)
4. 定期更新系统和依赖包
5. 限制文件上传大小和类型
