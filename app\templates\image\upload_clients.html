<!-- 图片上传客户列表页面 -->
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="bi bi-image"></i> 图片上传</h2>
            <p class="text-muted">选择客户查看待上传图片的文案</p>
        </div>
    </div>

    <!-- 客户列表 -->
    <div class="card">
        <div class="card-body">
            {% if clients_with_pending %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>客户名称</th>
                                <th>待上传图片文案数量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in clients_with_pending %}
                            <tr>
                                <td>
                                    <strong>{{ client.name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-warning text-dark">
                                        {{ client.pending_count }} 篇待上传图片
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('main_simple.image_upload_client_detail', client_id=client.id) }}"
                                       class="btn btn-primary btn-sm">
                                        <i class="bi bi-image"></i> 上传图片
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-image text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">暂无待上传图片的文案</h4>
                    <p class="text-muted">所有客户的文案图片都已上传完成</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}
</style>
