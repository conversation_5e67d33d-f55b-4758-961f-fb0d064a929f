# 用户管理功能说明

## 功能概述

用户管理功能已经成功实现，包括以下特性：

### 1. 权限系统
- **角色权限**: 通过角色分配权限，支持多对多关系
- **直接权限**: 可以直接为用户分配特定权限
- **菜单权限**: 根据用户权限动态显示菜单项

### 2. 预设角色
系统已预设以下角色：

| 角色名称 | 描述 | 主要权限 |
|---------|------|----------|
| 超级管理员 | 拥有所有权限 | 所有功能模块 |
| 管理员 | 拥有大部分管理权限 | 除系统设置外的所有功能 |
| 编辑 | 拥有内容编辑权限 | 控制台、内容生成、内容审核、图片上传 |
| 审核员 | 拥有内容审核权限 | 控制台、内容审核、最终审核、客户审核 |
| 普通用户 | 基础查看权限 | 仅控制台 |

### 3. 菜单权限映射

| 菜单名称 | URL | 权限标识 |
|---------|-----|----------|
| 控制台 | `/simple/dashboard` | `dashboard.view` |
| 模板管理 | `/simple/templates` | `template.manage` |
| 客户管理 | `/simple/clients` | `client.manage` |
| 内容生成 | `/simple/content` | `content.generate` |
| 初审文案 | `/simple/review-content` | `content.review` |
| 图片上传 | `/simple/image-upload` | `image.upload` |
| 最终审核 | `/simple/final-review` | `content.final_review` |
| 客户审核 | `/simple/client-review` | `client.review` |
| 发布管理 | `/simple/publish-manage` | `publish.manage` |
| 发布状态 | `/simple/publish-status-manage` | `publish.status` |
| 用户管理 | `/simple/users` | `user.manage` |
| 系统设置 | `/simple/system` | `system.settings` |

## 使用方法

### 1. 访问用户管理页面
- 登录系统后，点击左侧菜单的"用户管理"
- 或直接访问：`http://127.0.0.1:5000/simple/users`

### 2. 用户管理功能
- **查看用户列表**: 支持搜索、筛选和分页
- **添加用户**: 点击"添加用户"按钮
- **编辑用户**: 点击用户行的编辑按钮
- **删除用户**: 点击用户行的删除按钮
- **启用/禁用用户**: 点击用户行的状态切换按钮

### 3. 权限分配
在添加或编辑用户时，可以：
- 分配角色（自动获得角色对应的权限）
- 直接分配权限（覆盖角色权限）
- 分配菜单权限（控制可见菜单）

### 4. 默认超级管理员
系统已创建默认超级管理员账户：
- 用户名：`admin`
- 密码：`admin123`
- 邮箱：`<EMAIL>`

## 技术实现

### 1. 数据库表结构
- `users`: 用户基本信息
- `roles`: 角色信息
- `permissions`: 权限信息
- `menu_items`: 菜单项信息
- `user_roles`: 用户角色关联表
- `role_permissions`: 角色权限关联表
- `user_permissions`: 用户直接权限关联表

### 2. 核心功能
- **权限检查**: `User.has_permission(permission_name)`
- **菜单获取**: `User.get_menu_items()`
- **角色检查**: `User.has_role(role_name)`

### 3. 前端实现
- 动态菜单显示：根据用户权限显示对应菜单
- 权限验证：在访问页面时验证用户权限
- 用户界面：现代化的Bootstrap 5界面

## 安全特性

1. **密码加密**: 使用Werkzeug的密码哈希功能
2. **权限验证**: 每个页面都有权限检查
3. **角色隔离**: 不同角色只能看到对应的功能
4. **软删除**: 支持用户状态管理而不是物理删除

## 扩展说明

如需添加新的权限或菜单项：

1. 在数据库中插入新的权限记录
2. 在菜单表中添加对应的菜单项
3. 为角色分配新权限
4. 更新前端权限检查逻辑

## 注意事项

1. 超级管理员拥有所有权限，无法被删除或禁用
2. 用户不能删除自己的账户
3. 权限变更会立即生效，无需重启应用
4. 建议定期备份用户和权限数据 