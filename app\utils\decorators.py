"""
装饰器工具
"""
from functools import wraps
from flask import request, abort, current_app, jsonify, render_template, flash, redirect, url_for, get_flashed_messages
from flask_login import current_user


def permission_required(permission):
    """
    权限检查装饰器
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('请先登录', 'warning')
                return redirect(url_for('auth.login'))

            if not current_user.has_permission(permission):
                flash('您没有权限执行此操作', 'danger')
                return abort(403)

            return f(*args, **kwargs)
        return decorated_function
    return decorator


def menu_permission_required(url_path):
    """
    菜单权限检查装饰器 - 检查用户是否有访问指定URL的菜单权限
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('请先登录', 'warning')
                return redirect(url_for('auth.login'))

            # 检查用户是否有对应的菜单权限
            user_menu_items = current_user.get_menu_items()
            has_menu_permission = any(menu.url == url_path for menu in user_menu_items)

            if not has_menu_permission:
                flash('您没有权限访问此页面', 'danger')
                return abort(403)

            return f(*args, **kwargs)
        return decorated_function
    return decorator


def admin_required(f):
    """
    检查用户是否为管理员的装饰器
    
    Returns:
        装饰器函数
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login'))
        
        if not current_user.is_admin:
            flash('只有管理员可以访问此页面', 'danger')
            return abort(403)
        
        return f(*args, **kwargs)
    return decorated_function


def ajax_aware(f):
    """
    AJAX请求感知装饰器
    如果请求头中包含X-Requested-With: XMLHttpRequest，则返回主内容区域的HTML
    否则返回完整页面
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        result = f(*args, **kwargs)
        
        # 检查是否为AJAX请求
        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
        
        # 如果结果是重定向响应
        if isinstance(result, tuple) and len(result) > 0 and isinstance(result[0], str) and result[0].startswith('redirect:'):
            redirect_url = result[0][9:]  # 去掉 'redirect:' 前缀
            if is_ajax:
                return jsonify({'redirect': redirect_url})
            else:
                return redirect(redirect_url)
        
        # 如果结果是Flask的重定向响应
        if hasattr(result, 'status_code') and 300 <= result.status_code < 400:
            if is_ajax:
                return jsonify({'redirect': result.location})
            else:
                return result
        
        # 如果结果不是元组，则假设它是模板名称和上下文
        if not isinstance(result, tuple):
            return result
        
        template, context = result
        
        # 对于AJAX请求，只返回内容区域的HTML
        if is_ajax:
            # 获取闪现消息
            flashed_messages = []
            with current_app.test_request_context():
                messages = []
                for category, message in get_flashed_messages(with_categories=True):
                    messages.append({
                        'category': category,
                        'message': message
                    })
                if messages:
                    flashed_messages = render_template('_flashed_messages.html', messages=messages)
            
            # 渲染模板，但不包含基础布局
            content_html = render_template(template, **context)
            
            # 返回JSON响应
            response = jsonify({
                'html': content_html,
                'title': context.get('title', 'Small Red Book Content Generator'),
                'flash_messages': flashed_messages
            })
            
            # 设置正确的Content-Type头
            response.headers['Content-Type'] = 'application/json'
            return response
        
        # 对于普通请求，正常渲染完整页面
        return render_template(template, **context)
    
    return decorated_function 