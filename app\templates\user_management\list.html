{% extends "base_simple.html" %}

{% block title %}用户管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-people me-2"></i>用户管理
                    </h5>
                    <a href="{{ url_for('user_management.add_user') }}" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus me-1"></i>添加用户
                    </a>
                </div>
                <div class="card-body">
                    <!-- 搜索和筛选 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <form method="GET" class="d-flex">
                                <input type="text" class="form-control" name="search" 
                                       placeholder="搜索用户名、邮箱或姓名" value="{{ search }}">
                                <button type="submit" class="btn btn-outline-secondary ms-2">
                                    <i class="bi bi-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" name="role" onchange="this.form.submit()">
                                <option value="">所有角色</option>
                                {% for role in roles %}
                                <option value="{{ role.name }}" {% if role_filter == role.name %}selected{% endif %}>
                                    {{ role.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" name="status" onchange="this.form.submit()">
                                <option value="">所有状态</option>
                                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>启用</option>
                                <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>禁用</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            {% if search or role_filter or status_filter %}
                            <a href="{{ url_for('user_management.user_list') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-x me-1"></i>清除筛选
                            </a>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 用户列表 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>姓名</th>
                                    <th>权限</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>最后登录</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in pagination.items %}
                                <tr>
                                    <td>{{ user.id }}</td>
                                    <td>
                                        <strong>{{ user.username }}</strong>
                                        {% if user.phone %}
                                        <br><small class="text-muted">{{ user.phone }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ user.email }}</td>
                                    <td>{{ user.real_name or '-' }}</td>
                                    <td>
                                        {% for menu in user.menu_permissions %}
                                        <span class="badge bg-secondary me-1">{{ menu.name }}</span>
                                        {% endfor %}
                                        {% if not user.menu_permissions %}
                                        <span class="text-muted">无权限</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                        <span class="badge bg-success">启用</span>
                                        {% else %}
                                        <span class="badge bg-danger">禁用</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if user.last_login %}
                                        {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                        <span class="text-muted">从未登录</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('user_management.edit_user', user_id=user.id) }}" 
                                               class="btn btn-outline-primary" title="编辑">
                                                编辑
                                            </a>
                                            {% if user.id != current_user.id %}
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteUser({{ user.id }}, '{{ user.username }}')" title="删除">
                                                删除
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="用户列表分页">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('user_management.user_list', page=pagination.prev_num, search=search, role=role_filter, status=status_filter) }}">
                                    上一页
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('user_management.user_list', page=page_num, search=search, role=role_filter, status=status_filter) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('user_management.user_list', page=pagination.next_num, search=search, role=role_filter, status=status_filter) }}">
                                    下一页
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    <!-- 统计信息 -->
                    <div class="text-muted text-center">
                        共 {{ pagination.total }} 个用户，当前显示第 {{ pagination.page }} 页，每页 {{ pagination.per_page }} 条
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast 通知 -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="toast-title">通知</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toast-message">
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// 显示Toast通知
function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    const toastTitle = document.getElementById('toast-title');
    const toastMessage = document.getElementById('toast-message');
    
    // 设置标题和消息
    toastTitle.textContent = type === 'success' ? '成功' : type === 'error' ? '错误' : '通知';
    toastMessage.textContent = message;
    
    // 设置样式
    toast.className = `toast ${type === 'success' ? 'bg-success text-white' : type === 'error' ? 'bg-danger text-white' : ''}`;
    
    // 显示Toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

// 删除用户
function deleteUser(userId, username) {
    if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复！`)) {
        return;
    }
    
    fetch(`/simple/users/${userId}/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('删除失败，请重试', 'error');
    });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 自动提交筛选表单
    const filterSelects = document.querySelectorAll('select[name="role"], select[name="status"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});

</script>
{% endblock %}