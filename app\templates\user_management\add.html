{% extends "base_simple.html" %}

{% block title %}添加用户{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">
                        <i class="bi bi-person-plus me-2"></i>添加用户
                    </h4>
                    <a href="{{ url_for('user_management.user_list') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-arrow-left me-1"></i>返回列表
                    </a>
                </div>
                <div class="card-body">
                    <form id="addUserForm">
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <h6 class="mb-3">基本信息</h6>
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                    <div class="form-text">用户名必须唯一，用于登录</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">邮箱 <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">密码 <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <div class="form-text">密码长度至少6位</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">确认密码 <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="real_name" class="form-label">真实姓名</label>
                                    <input type="text" class="form-control" id="real_name" name="real_name">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="phone" class="form-label">手机号码</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                        <label class="form-check-label" for="is_active">
                                            启用账户
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 权限分配 -->
                            <div class="col-md-6">
                                <h6 class="mb-3">权限分配</h6>
                                
                                <!-- 菜单权限分配 -->
                                <div class="mb-3">
                                    <label class="form-label">菜单权限</label>
                                    <div class="border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                                        {% for menu in menu_items %}
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="menu_{{ menu.id }}" 
                                                   name="menu_permissions" value="{{ menu.id }}">
                                            <label class="form-check-label" for="menu_{{ menu.id }}">
                                                <i class="{{ menu.icon or 'bi bi-link' }} me-1"></i>
                                                {{ menu.name }}
                                                {% if menu.permission %}
                                                <br><small class="text-muted">权限: {{ menu.permission }}</small>
                                                {% endif %}
                                            </label>
                                        </div>
                                        {% endfor %}
                                        {% if not menu_items %}
                                        <p class="text-muted">暂无可用菜单</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-end">
                                    <a href="{{ url_for('user_management.user_list') }}" class="btn btn-secondary me-2">取消</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-1"></i>保存用户
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast 通知 -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="toast-title">通知</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toast-message">
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 显示Toast通知
function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    const toastTitle = document.getElementById('toast-title');
    const toastMessage = document.getElementById('toast-message');
    
    toastTitle.textContent = type === 'success' ? '成功' : type === 'error' ? '错误' : '通知';
    toastMessage.textContent = message;
    
    toast.className = `toast ${type === 'success' ? 'bg-success text-white' : type === 'error' ? 'bg-danger text-white' : ''}`;
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

// 表单验证
function validateForm() {
    const username = document.getElementById('username').value.trim();
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (!username) {
        showToast('请输入用户名', 'error');
        return false;
    }
    
    if (!email) {
        showToast('请输入邮箱', 'error');
        return false;
    }
    
    if (!password) {
        showToast('请输入密码', 'error');
        return false;
    }
    
    if (password.length < 6) {
        showToast('密码长度至少6位', 'error');
        return false;
    }
    
    if (password !== confirmPassword) {
        showToast('两次输入的密码不一致', 'error');
        return false;
    }
    
    return true;
}

// 获取表单数据
function getFormData() {
    const form = document.getElementById('addUserForm');
    const formData = new FormData(form);
    
    const data = {
        username: formData.get('username'),
        email: formData.get('email'),
        password: formData.get('password'),
        real_name: formData.get('real_name') || '',
        phone: formData.get('phone') || '',
        is_active: formData.get('is_active') === 'on',
        menu_permissions: []
    };
    
    // 获取选中的菜单权限
    const selectedMenus = form.querySelectorAll('input[name="menu_permissions"]:checked');
    selectedMenus.forEach(menu => {
        data.menu_permissions.push(parseInt(menu.value));
    });
    
    return data;
}

// 提交表单
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (!validateForm()) {
        return;
    }
    
    const data = getFormData();
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // 禁用提交按钮
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>保存中...';
    
    fetch('{{ url_for("user_management.add_user") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            setTimeout(() => {
                window.location.href = '{{ url_for("user_management.user_list") }}';
            }, 1500);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('保存失败，请重试', 'error');
    })
    .finally(() => {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 密码确认验证
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (confirmPassword.value && password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('密码不匹配');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('input', validatePassword);
    confirmPassword.addEventListener('input', validatePassword);
});
</script>
{% endblock %} 